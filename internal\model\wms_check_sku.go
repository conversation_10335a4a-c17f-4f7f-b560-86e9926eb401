package model

import (
	"gorm.io/gorm"
)

// 盘点单物料明细表，用于管理仓库的盘点单物料明细信息
type WmsCheckSku struct {
	gorm.Model
	CheckId   uint `gorm:"default:0; index; comment:盘点单ID"`
	SkuId     uint `gorm:"default:0; index; comment:物料规格ID"`
	Num       uint `gorm:"default:0; index; comment:库存数量"`
	Remain    uint `gorm:"default:0; index; comment:实际库存"`
	Status    bool `gorm:"default:false; index; comment:状态"`
	TenantId  uint `gorm:"default:0; index; comment:租户ID"`
	CreatedBy uint `gorm:"size:64; comment:创建人"`
	UpdatedBy uint `gorm:"size:64; comment:操作人"`
}

func (m *WmsCheckSku) TableName() string {
	return "wms_check_sku"
}
