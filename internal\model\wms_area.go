package model

import (
	"github.com/lib/pq"
	"gorm.io/gorm"
)

// 仓库区域表，用于管理仓库的区域信息
type WmsArea struct {
	gorm.Model
	Name       string        `gorm:"size:64; not null; index; comment:名称"`
	Code       string        `gorm:"size:64; not null; index; comment:编号"`
	Type       uint          `gorm:"default:0; index; comment:类型 1-仓库 2-库区 3-库位"`
	Summary    string        `gorm:"type:text; comment:备注"`
	Order      int           `gorm:"default:0; index; comment:排序"`
	Status     bool          `gorm:"default:false; index; comment:状态"`
	ParentId   uint          `gorm:"default:0; index; comment:父级ID"`
	ParentPath pq.Int64Array `gorm:"type:integer[]; index; comment:父级路径"`
	TenantId   uint          `gorm:"default:0; index; comment:租户ID"`
	CreatedBy  uint          `gorm:"size:64; comment:创建人"`
	UpdatedBy  uint          `gorm:"size:64; comment:操作人"`
}

func (m *WmsArea) TableName() string {
	return "wms_area"
}
