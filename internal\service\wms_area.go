package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/jinzhu/copier"
)

type WmsAreaService interface {
	Create(ctx context.Context, req *v1.WmsAreaCreateParams) error
	Update(ctx context.Context, id uint, req *v1.WmsAreaUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.WmsAreaResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewWmsAreaService(
	service *Service,
	wmsAreaRepository repository.WmsAreaRepository,
) WmsAreaService {
	return &wmsAreaService{
		Service:           service,
		wmsAreaRepository: wmsAreaRepository,
	}
}

type wmsAreaService struct {
	*Service
	wmsAreaRepository repository.WmsAreaRepository
}

// 仓库区域相关方法实现
func (s *wmsAreaService) Create(ctx context.Context, req *v1.WmsAreaCreateParams) error {
	area := &model.WmsArea{}
	if err := copier.Copy(area, req); err != nil {
		return err
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsAreaRepository.Create(ctx, area); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsAreaService) Update(ctx context.Context, id uint, req *v1.WmsAreaUpdateParams) error {
	area, err := s.wmsAreaRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	if err := copier.Copy(area, req); err != nil {
		return err
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsAreaRepository.Update(ctx, area); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsAreaService) Delete(ctx context.Context, id uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsAreaRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsAreaService) BatchDelete(ctx context.Context, ids []uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.wmsAreaRepository.BatchDelete(ctx, ids); err != nil {
			return err
		}
		return nil
	})
}

func (s *wmsAreaService) Get(ctx context.Context, id uint) (*v1.WmsAreaResponse, error) {
	area, err := s.wmsAreaRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	response := &v1.WmsAreaResponse{}
	if err := copier.Copy(response, area); err != nil {
		return nil, err
	}

	response.CreatedAt = area.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = area.UpdatedAt.Format(time.RFC3339)

	return response, nil
}

func (s *wmsAreaService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	areas, total, err := s.wmsAreaRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.WmsAreaResponse, 0, len(areas))
	for _, area := range areas {
		response := &v1.WmsAreaResponse{}
		if err := copier.Copy(response, area); err != nil {
			return nil, err
		}

		response.CreatedAt = area.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = area.UpdatedAt.Format(time.RFC3339)

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
