package model

import "gorm.io/gorm"

// 物料包装表，用于管理物料的包装信息
type WmsPackage struct {
	gorm.Model
	Code         string `gorm:"size:64; not null; index; comment:编号"`
	DeliverSkuId uint   `gorm:"default:0; index; comment:出库物料ID"`
	TenantId     uint   `gorm:"default:0; index; comment:租户ID"`
	CreatedBy    uint   `gorm:"size:64; comment:创建人"`
	UpdatedBy    uint   `gorm:"size:64; comment:操作人"`
}

func (m *WmsPackage) TableName() string {
	return "wms_package"
}
