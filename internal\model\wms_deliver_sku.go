package model

import (
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// 出库单物料明细表，用于管理仓库的出库单物料明细信息
type WmsDeliverSku struct {
	gorm.Model
	DeliverId uint           `gorm:"default:0; index; comment:出库单ID"`
	SkuId     uint           `gorm:"default:0; index; comment:物料规格ID"`
	Num       uint           `gorm:"default:0; index; comment:数量"`
	AreaNum   datatypes.JSON `gorm:"type:jsonb; comment:出库配置"`
	Pcs       uint           `gorm:"default:0; index; comment:包装规格"`
	Status    bool           `gorm:"default:false; index; comment:状态"`
	TenantId  uint           `gorm:"default:0; index; comment:租户ID"`
	CreatedBy uint           `gorm:"size:64; comment:创建人"`
	UpdatedBy uint           `gorm:"size:64; comment:操作人"`
}

func (m *WmsDeliverSku) TableName() string {
	return "wms_deliver_sku"
}
