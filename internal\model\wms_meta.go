package model

import (
	"github.com/lib/pq"
	"gorm.io/gorm"
)

// 物料分类表，用于管理物料的分类信息
type WmsMeta struct {
	gorm.Model
	Name       string        `gorm:"size:64; not null; index; comment:名称"`
	Order      int           `gorm:"default:0; index; comment:排序"`
	Status     bool          `gorm:"default:false; index; comment:状态"`
	ParentId   uint          `gorm:"default:0; index; comment:父级ID"`
	ParentPath pq.Int64Array `gorm:"type:integer[]; index; comment:父级路径"`
	TenantId   uint          `gorm:"default:0; index; comment:租户ID"`
	CreatedBy  uint          `gorm:"size:64; comment:创建人"`
	UpdatedBy  uint          `gorm:"size:64; comment:操作人"`
}

func (m *WmsMeta) TableName() string {
	return "wms_meta"
}
