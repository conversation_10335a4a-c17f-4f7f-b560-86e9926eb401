package model

import "gorm.io/gorm"

// 库存日志表，用于管理库存的日志信息
type WmsStockLog struct {
	gorm.Model
	SkuId     uint   `gorm:"default:0; index; comment:物料规格ID"`
	Type      uint   `gorm:"default:0; index; comment:类型 1-入库 2-出库 3-调拨 4-盘点 5-其它"`
	RelatedNo string `gorm:"size:64; not null; index; comment:关联单号"`
	Summary   string `gorm:"type:text; comment:描述"`
	TenantId  uint   `gorm:"default:0; index; comment:租户ID"`
	CreatedBy uint   `gorm:"size:64; comment:创建人"`
	UpdatedBy uint   `gorm:"size:64; comment:操作人"`
}

func (m *WmsStockLog) TableName() string {
	return "wms_stock_log"
}
