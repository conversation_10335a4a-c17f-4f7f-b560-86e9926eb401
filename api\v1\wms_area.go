package v1

import "github.com/lib/pq"

type WmsAreaCreateParams struct {
	Name       string        `json:"name" binding:"required,max=64" example:"仓库A"`
	Code       string        `json:"code" binding:"required,max=64" example:"WH001"`
	Type       uint          `json:"type" binding:"required" example:"1"`
	Summary    string        `json:"summary" example:"主仓库"`
	Order      int           `json:"order" example:"1"`
	Status     bool          `json:"status" example:"true"`
	ParentId   uint          `json:"parentId" example:"0"`
	ParentPath pq.Int64Array `json:"parentPath" swaggertype:"array,integer" example:"1,2,3"`
	TenantId   uint          `json:"tenantId" example:"1"`
	CreatedBy  uint          `json:"createdBy" example:"1"`
	UpdatedBy  uint          `json:"updatedBy" example:"1"`
}

type WmsAreaUpdateParams struct {
	WmsAreaCreateParams
}

type WmsAreaResponse struct {
	ID         uint          `json:"id"`
	Name       string        `json:"name"`
	Code       string        `json:"code"`
	Type       uint          `json:"type"`
	Summary    string        `json:"summary"`
	Order      int           `json:"order"`
	Status     bool          `json:"status"`
	ParentId   uint          `json:"parentId"`
	ParentPath pq.Int64Array `json:"parentPath"`
	TenantId   uint          `json:"tenantId"`
	CreatedBy  uint          `json:"createdBy"`
	UpdatedBy  uint          `json:"updatedBy"`
	CreatedAt  string        `json:"createdAt"`
	UpdatedAt  string        `json:"updatedAt"`
}
