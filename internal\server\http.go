package server

import (
	v1 "daisy-server/api/v1"
	"daisy-server/docs"
	"daisy-server/internal/handler"
	"daisy-server/internal/middleware"
	"daisy-server/internal/repository"
	"daisy-server/pkg/jwt"
	"daisy-server/pkg/log"
	"daisy-server/pkg/rbac"
	"daisy-server/pkg/server/http"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	swaggerfiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

func NewHTTPServer(
	logger *log.Logger,
	conf *viper.Viper,
	jwt *jwt.JWT,
	rbac *rbac.RBAC,
	sysLogRepo repository.SysLogRepository,
	authHandler *handler.AuthHandler,
	uploadHandler *handler.Upload<PERSON>andler,
	sysConfigHandler *handler.SysConfig<PERSON><PERSON><PERSON>,
	sysDictHandler *handler.<PERSON>ys<PERSON><PERSON><PERSON><PERSON><PERSON>,
	sysMenuHandler *handler.<PERSON>ys<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	sysApiHandler *handler.<PERSON>ys<PERSON><PERSON><PERSON><PERSON><PERSON>,
	sysRoleHandler *handler.SysRoleHandler,
	sysUserHandler *handler.SysUserHandler,
	sysTenantHandler *handler.SysTenantHandler,
	sysLogHandler *handler.SysLogHandler,
	cmsMetaHandler *handler.CmsMetaHandler,
	cmsPostHandler *handler.CmsPostHandler,
	wmsAreaHandler *handler.WmsAreaHandler,
) *http.Server {
	if conf.GetString("env") == "prod" {
		gin.SetMode(gin.ReleaseMode)
	}
	s := http.NewServer(
		gin.Default(),
		logger,
		http.WithServerHost(conf.GetString("http.host")),
		http.WithServerPort(conf.GetInt("http.port")),
	)

	// swagger doc
	docs.SwaggerInfo.BasePath = "/v1"
	s.GET("/swagger/*any", ginSwagger.WrapHandler(
		swaggerfiles.Handler,
		//ginSwagger.URL(fmt.Sprintf("http://localhost:%d/swagger/doc.json", conf.GetInt("app.http.port"))),
		ginSwagger.DefaultModelsExpandDepth(-1),
		ginSwagger.PersistAuthorization(true),
	))

	s.Use(
		middleware.CORSMiddleware(),
		middleware.RequestLogMiddleware(logger),
		middleware.ResponseLogMiddleware(logger, sysLogRepo),
		//middleware.SignMiddleware(log),
	)
	s.GET("/", func(ctx *gin.Context) {
		logger.WithContext(ctx).Info("hello")
		v1.HandleSuccess(ctx, map[string]interface{}{
			":)": "Thank you for using nunu!",
		})
	})

	v1 := s.Group("/v1")
	{
		// 公共模块
		noAuthRouter := v1.Group("/")
		{
			noAuthRouter.POST("/auth/login", authHandler.Login)
			noAuthRouter.POST("/auth/logout", authHandler.Logout)
			noAuthRouter.POST("/auth/refresh", authHandler.Refresh)
			noAuthRouter.GET("/auth/constant_routes", authHandler.ConstantRoutes)
			noAuthRouter.GET("/auth/exist_route", authHandler.ExistRoute)
		}

		// 认证模块 - 添加RBAC中间件
		authRouter := v1.Group("/auth").Use(middleware.StrictAuth(jwt, logger), middleware.RBACAuth(rbac, logger))
		{
			authRouter.GET("/userinfo", authHandler.Userinfo)
			authRouter.POST("/userinfo", authHandler.UpdateUserinfo)
			authRouter.POST("/password", authHandler.UpdatePassword)
			authRouter.GET("/user_routes", authHandler.UserRoutes)
		}

		// 上传模块 - 添加RBAC中间件
		uploadRouter := v1.Group("/upload").Use(middleware.StrictAuth(jwt, logger), middleware.RBACAuth(rbac, logger))
		{
			uploadRouter.POST("/image", uploadHandler.UploadImage)
			uploadRouter.POST("/file", uploadHandler.UploadFile)
			uploadRouter.DELETE("/remove", uploadHandler.UploadRemove)
		}

		// 系统模块 - 添加RBAC中间件
		sysRouter := v1.Group("/system").Use(middleware.StrictAuth(jwt, logger), middleware.RBACAuth(rbac, logger))
		{
			// 配置管理
			sysRouter.POST("/configs", sysConfigHandler.Create)
			sysRouter.PATCH("/configs/:id", sysConfigHandler.Update)
			sysRouter.DELETE("/configs/:id", sysConfigHandler.Delete)
			sysRouter.DELETE("/configs", sysConfigHandler.BatchDelete)
			sysRouter.GET("/configs/:id", sysConfigHandler.Get)
			sysRouter.GET("/configs", sysConfigHandler.List)

			// 字典管理
			sysRouter.POST("/dicts", sysDictHandler.Create)
			sysRouter.PATCH("/dicts/:id", sysDictHandler.Update)
			sysRouter.DELETE("/dicts/:id", sysDictHandler.Delete)
			sysRouter.DELETE("/dicts", sysDictHandler.BatchDelete)
			sysRouter.GET("/dicts/:id", sysDictHandler.Get)
			sysRouter.GET("/dicts", sysDictHandler.List)

			// 菜单管理
			sysRouter.POST("/menus", sysMenuHandler.Create)
			sysRouter.PATCH("/menus/:id", sysMenuHandler.Update)
			sysRouter.DELETE("/menus/:id", sysMenuHandler.Delete)
			sysRouter.DELETE("/menus", sysMenuHandler.BatchDelete)
			sysRouter.GET("/menus/:id", sysMenuHandler.Get)
			sysRouter.GET("/menus", sysMenuHandler.List)

			// 接口管理
			sysRouter.POST("/apis", sysApiHandler.Create)
			sysRouter.PATCH("/apis/:id", sysApiHandler.Update)
			sysRouter.DELETE("/apis/:id", sysApiHandler.Delete)
			sysRouter.DELETE("/apis", sysApiHandler.BatchDelete)
			sysRouter.GET("/apis/:id", sysApiHandler.Get)
			sysRouter.GET("/apis", sysApiHandler.List)
			sysRouter.GET("/apis/refresh", sysApiHandler.Refresh)

			// 角色管理
			sysRouter.POST("/roles", sysRoleHandler.Create)
			sysRouter.PATCH("/roles/:id", sysRoleHandler.Update)
			sysRouter.DELETE("/roles/:id", sysRoleHandler.Delete)
			sysRouter.DELETE("/roles", sysRoleHandler.BatchDelete)
			sysRouter.GET("/roles/:id", sysRoleHandler.Get)
			sysRouter.GET("/roles", sysRoleHandler.List)
			sysRouter.PATCH("/roles/:id/permit", sysRoleHandler.Permit)

			// 用户管理
			sysRouter.POST("/users", sysUserHandler.Create)
			sysRouter.PATCH("/users/:id", sysUserHandler.Update)
			sysRouter.DELETE("/users/:id", sysUserHandler.Delete)
			sysRouter.DELETE("/users", sysUserHandler.BatchDelete)
			sysRouter.GET("/users/:id", sysUserHandler.Get)
			sysRouter.GET("/users", sysUserHandler.List)

			// 租户管理
			sysRouter.POST("/tenants", sysTenantHandler.Create)
			sysRouter.PATCH("/tenants/:id", sysTenantHandler.Update)
			sysRouter.DELETE("/tenants/:id", sysTenantHandler.Delete)
			sysRouter.DELETE("/tenants", sysTenantHandler.BatchDelete)
			sysRouter.GET("/tenants/:id", sysTenantHandler.Get)
			sysRouter.GET("/tenants", sysTenantHandler.List)

			// 日志管理
			sysRouter.DELETE("/logs/:id", sysLogHandler.Delete)
			sysRouter.DELETE("/logs", sysLogHandler.BatchDelete)
			sysRouter.GET("/logs/:id", sysLogHandler.Get)
			sysRouter.GET("/logs", sysLogHandler.List)
		}

		// 内容模块
		cmsRouter := v1.Group("/cms").Use(middleware.StrictAuth(jwt, logger), middleware.RBACAuth(rbac, logger))
		{
			// 栏目管理
			cmsRouter.POST("/metas", cmsMetaHandler.Create)
			cmsRouter.PATCH("/metas/:id", cmsMetaHandler.Update)
			cmsRouter.DELETE("/metas/:id", cmsMetaHandler.Delete)
			cmsRouter.DELETE("/metas", cmsMetaHandler.BatchDelete)
			cmsRouter.GET("/metas/:id", cmsMetaHandler.Get)
			cmsRouter.GET("/metas", cmsMetaHandler.List)

			// 文章管理
			cmsRouter.POST("/posts", cmsPostHandler.Create)
			cmsRouter.PATCH("/posts/:id", cmsPostHandler.Update)
			cmsRouter.DELETE("/posts/:id", cmsPostHandler.Delete)
			cmsRouter.DELETE("/posts", cmsPostHandler.BatchDelete)
			cmsRouter.GET("/posts/:id", cmsPostHandler.Get)
			cmsRouter.GET("/posts", cmsPostHandler.List)
		}

		// 仓储模块
		wmsRouter := v1.Group("/wms").Use(middleware.StrictAuth(jwt, logger), middleware.RBACAuth(rbac, logger))
		{
			// 仓库管理
			wmsRouter.POST("/areas", wmsAreaHandler.Create)
			wmsRouter.PATCH("/areas/:id", wmsAreaHandler.Update)
			wmsRouter.DELETE("/areas/:id", wmsAreaHandler.Delete)
			wmsRouter.DELETE("/areas", wmsAreaHandler.BatchDelete)
			wmsRouter.GET("/areas/:id", wmsAreaHandler.Get)
			wmsRouter.GET("/areas", wmsAreaHandler.List)
		}
	}

	return s
}
