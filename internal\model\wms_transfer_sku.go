package model

import (
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// 调拨单物料明细表，用于管理仓库的调拨单物料明细信息
type WmsTransferSku struct {
	gorm.Model
	TransferId  uint           `gorm:"default:0; index; comment:调拨单ID"`
	SkuId       uint           `gorm:"default:0; index; comment:物料规格ID"`
	Num         uint           `gorm:"default:0; index; comment:数量"`
	FromAreaNum datatypes.JSON `gorm:"type:jsonb; comment:来源库配置"`
	ToAreaNum   datatypes.JSON `gorm:"type:jsonb; comment:目标库配置"`
	Status      bool           `gorm:"default:false; index; comment:状态"`
	TenantId    uint           `gorm:"default:0; index; comment:租户ID"`
	CreatedBy   uint           `gorm:"size:64; comment:创建人"`
	UpdatedBy   uint           `gorm:"size:64; comment:操作人"`
}

func (m *WmsTransferSku) TableName() string {
	return "wms_transfer_sku"
}
