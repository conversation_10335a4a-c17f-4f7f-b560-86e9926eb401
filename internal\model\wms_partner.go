package model

import (
	"github.com/lib/pq"
	"gorm.io/gorm"
)

// 合作伙伴表，用于管理物料的合作伙伴信息
type WmsPartner struct {
	gorm.Model
	Name        string        `gorm:"size:64; not null; index; comment:名称"`
	Type        pq.Int64Array `gorm:"type:integer[]; index; comment:类型 1-供应商 2-客户 3-其它"`
	Level       uint          `gorm:"default:0; index; comment:级别"`
	Summary     string        `gorm:"type:text; comment:备注"`
	Contact     string        `gorm:"size:64; index; comment:联系人"`
	Phone       string        `gorm:"size:20; index; comment:手机号"`
	Email       string        `gorm:"size:128; index; comment:邮箱"`
	Area        string        `gorm:"size:32; comment:地区"`
	Address     string        `gorm:"size:255; comment:地址"`
	BankName    string        `gorm:"size:128; index; comment:开户行"`
	BankAccount string        `gorm:"size:128; index; comment:银行账户"`
	Order       int           `gorm:"default:0; index; comment:排序"`
	Status      bool          `gorm:"default:false; index; comment:状态"`
	TenantId    uint          `gorm:"default:0; index; comment:租户ID"`
	CreatedBy   uint          `gorm:"size:64; comment:创建人"`
	UpdatedBy   uint          `gorm:"size:64; comment:操作人"`
}

func (m *WmsPartner) TableName() string {
	return "wms_partner"
}
