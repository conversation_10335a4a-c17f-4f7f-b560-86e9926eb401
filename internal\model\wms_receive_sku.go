package model

import (
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// 入库单物料明细表，用于管理仓库的入库单物料明细信息
type WmsReceiveSku struct {
	gorm.Model
	ReceiveId uint           `gorm:"default:0; index; comment:入库单ID"`
	SkuId     uint           `gorm:"default:0; index; comment:物料规格ID"`
	BatchNo   string         `gorm:"size:64; not null; index; comment:批次号"`
	Num       uint           `gorm:"default:0; index; comment:数量"`
	AreaNum   datatypes.JSON `gorm:"type:jsonb; comment:入库配置"`
	Status    bool           `gorm:"default:false; index; comment:状态"`
	TenantId  uint           `gorm:"default:0; index; comment:租户ID"`
	CreatedBy uint           `gorm:"size:64; comment:创建人"`
	UpdatedBy uint           `gorm:"size:64; comment:操作人"`
}

func (m *WmsReceiveSku) TableName() string {
	return "wms_receive_sku"
}
