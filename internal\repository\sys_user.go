package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type SysUserRepository interface {
	Create(ctx context.Context, user *model.SysUser) error
	Update(ctx context.Context, user *model.SysUser) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.SysUser, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.SysUser, int64, error)
}

func NewSysUserRepository(
	repository *Repository,
) SysUserRepository {
	return &sysUserRepository{
		Repository: repository,
	}
}

type sysUserRepository struct {
	*Repository
}

func (r *sysUserRepository) Create(ctx context.Context, user *model.SysUser) error {
	if err := r.DB(ctx).Create(user).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysUserRepository) Update(ctx context.Context, user *model.SysUser) error {
	if err := r.DB(ctx).Save(user).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysUserRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.SysUser{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysUserRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.SysUser{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysUserRepository) Get(ctx context.Context, id uint) (*model.SysUser, error) {
	var user model.SysUser
	if err := r.DB(ctx).First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &user, nil
}

func (r *sysUserRepository) List(ctx context.Context, params *pagination.Params) ([]*model.SysUser, int64, error) {
	var records []*model.SysUser
	var total int64

	db := r.DB(ctx).Model(&model.SysUser{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.SysUser{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
