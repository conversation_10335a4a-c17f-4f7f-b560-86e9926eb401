package model

import (
	"github.com/lib/pq"
	"gorm.io/gorm"
)

// 盘点单表，用于管理仓库的盘点单信息
type WmsCheck struct {
	gorm.Model
	Code      string        `gorm:"size:64; not null; index; comment:编号"`
	Type      uint          `gorm:"default:0; index; comment:类型"`
	AreaPath  pq.Int64Array `gorm:"type:integer[]; index; comment:库区路径"`
	Summary   string        `gorm:"type:text; comment:备注"`
	Status    uint          `gorm:"default:1; index; comment:状态 1草稿 2待审核 3作业中 4已完成 5已作废"`
	TenantId  uint          `gorm:"default:0; index; comment:租户ID"`
	CreatedBy uint          `gorm:"size:64; comment:创建人"`
	UpdatedBy uint          `gorm:"size:64; comment:操作人"`
}

func (m *WmsCheck) TableName() string {
	return "wms_check"
}
