package v1

var (
	// common errors
	ErrSuccess             = newError(200, "成功")
	ErrBadRequest          = newError(400, "请求参数错误")
	ErrUnauthorized        = newError(401, "未授权")
	ErrForbidden           = newError(403, "禁止访问")
	ErrNotFound            = newError(404, "资源不存在")
	ErrInternalServerError = newError(500, "服务器内部错误")

	// more biz errors
	ErrEmailAlreadyUse      = newError(1001, "邮箱已被使用")
	ErrUsernameAlreadyUse   = newError(1002, "用户名已被使用")
	ErrInvalidToken         = newError(1003, "无效的令牌")
	ErrTokenExpired         = newError(1004, "令牌已过期")
	ErrPasswordIncorrect    = newError(1005, "密码不正确")
	ErrPasswordNotMatch     = newError(1006, "两次密码不一致")
	ErrParamBinding         = newError(1006, "参数绑定失败")
	ErrDataNotFound         = newError(1007, "数据不存在")
	ErrDatabaseOperation    = newError(1008, "数据库操作失败")
	ErrPermissionDenied     = newError(1009, "权限不足")
	ErrInvalidVerifyCode    = newError(1010, "验证码无效")
	ErrOperationTooFrequent = newError(1011, "操作过于频繁")
	ErrFileUploadFailed     = newError(1012, "文件上传失败")

	// 更多常用逻辑错误
	ErrRequestTimeout         = newError(1013, "请求超时")
	ErrServiceUnavailable     = newError(1014, "服务不可用")
	ErrInvalidFormat          = newError(1015, "格式无效")
	ErrAccountLocked          = newError(1016, "账号已锁定")
	ErrAccountDisabled        = newError(1017, "账号已禁用")
	ErrPaymentFailed          = newError(1018, "支付失败")
	ErrOrderNotFound          = newError(1019, "订单不存在")
	ErrBalanceInsufficient    = newError(1020, "余额不足")
	ErrRateLimitExceeded      = newError(1021, "请求频率超限")
	ErrFileTooBig             = newError(1022, "文件过大")
	ErrUnsupportedFileType    = newError(1023, "不支持的文件类型")
	ErrDataAlreadyExists      = newError(1024, "数据已存在")
	ErrOperationFailed        = newError(1025, "操作失败")
	ErrNetworkError           = newError(1026, "网络错误")
	ErrThirdPartyServiceError = newError(1027, "第三方服务错误")
	ErrConfigError            = newError(1028, "配置错误")
	ErrValidationFailed       = newError(1029, "数据验证失败")
	ErrVersionConflict        = newError(1030, "版本冲突")
)
